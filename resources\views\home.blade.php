@extends('layouts.app')

@section('title', 'Home - Portfolio')
@section('description', 'Welcome to my portfolio. I\'m a passionate web developer and software engineer creating innovative digital solutions.')

@section('content')
<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-blue-100 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23000000" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>
    
    <div class="container-custom section-padding relative z-10">
        <div class="text-center">
            <!-- Profile Image -->
            <div class="mb-8">
                <div class="w-32 h-32 mx-auto rounded-full bg-gradient-to-r from-primary-500 to-blue-600 p-1">
                    <div class="w-full h-full rounded-full bg-gray-200 flex items-center justify-center">
                        <svg class="w-16 h-16 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- Main Heading -->
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                Hi, I'm <span class="text-primary-600">John Doe</span>
            </h1>
            
            <!-- Subtitle -->
            <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
                A passionate <span class="font-semibold text-primary-600">Web Developer</span> & 
                <span class="font-semibold text-primary-600">Software Engineer</span> 
                creating innovative digital solutions
            </p>
            
            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <a href="{{ route('projects.index') }}" class="btn-primary text-lg px-8 py-3">
                    View My Work
                </a>
                <a href="{{ route('contact.index') }}" class="btn-secondary text-lg px-8 py-3">
                    Get In Touch
                </a>
            </div>
            
            <!-- Scroll Indicator -->
            <div class="animate-bounce">
                <svg class="w-6 h-6 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                </svg>
            </div>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
@if($featuredProjects->count() > 0)
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Projects</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Here are some of my recent projects that showcase my skills and expertise
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredProjects as $project)
            <div class="card group hover:shadow-lg transition-shadow duration-300">
                @if($project->image_url)
                <div class="mb-4 overflow-hidden rounded-lg">
                    <img src="{{ $project->image_url }}" alt="{{ $project->title }}" 
                         class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                </div>
                @endif
                
                <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $project->title }}</h3>
                <p class="text-gray-600 mb-4">{{ Str::limit($project->description, 120) }}</p>
                
                @if($project->technologies)
                <div class="flex flex-wrap gap-2 mb-4">
                    @foreach(array_slice($project->technologies, 0, 3) as $tech)
                    <span class="px-2 py-1 bg-primary-100 text-primary-700 text-sm rounded">{{ $tech }}</span>
                    @endforeach
                </div>
                @endif
                
                <div class="flex gap-3">
                    <a href="{{ route('projects.show', $project) }}" class="text-primary-600 hover:text-primary-700 font-medium">
                        View Details →
                    </a>
                    @if($project->live_url)
                    <a href="{{ $project->live_url }}" target="_blank" class="text-gray-600 hover:text-gray-700 font-medium">
                        Live Demo ↗
                    </a>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('projects.index') }}" class="btn-primary">
                View All Projects
            </a>
        </div>
    </div>
</section>
@endif

<!-- Skills Section -->
@if($featuredSkills->count() > 0)
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Skills & Expertise</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Technologies and tools I work with to bring ideas to life
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredSkills as $category => $skills)
            <div class="card">
                <h3 class="text-xl font-semibold text-gray-900 mb-4 capitalize">{{ str_replace('_', ' ', $category) }}</h3>
                <div class="space-y-3">
                    @foreach($skills->take(4) as $skill)
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-gray-700">{{ $skill->name }}</span>
                            <span class="text-sm text-gray-500">{{ $skill->proficiency }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full transition-all duration-500" 
                                 style="width: {{ $skill->proficiency }}%"></div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-12">
            <a href="{{ route('about') }}" class="btn-secondary">
                View All Skills
            </a>
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="section-padding bg-primary-600 text-white">
    <div class="container-custom text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Ready to Start Your Project?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Let's work together to bring your ideas to life. I'm always excited to take on new challenges.
        </p>
        <a href="{{ route('contact.index') }}" class="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200 inline-block">
            Get In Touch
        </a>
    </div>
</section>
@endsection
