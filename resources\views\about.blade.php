@extends('layouts.app')

@section('title', 'About - Portfolio')
@section('description', 'Learn more about my background, skills, and experience as a web developer and software engineer.')

@section('content')
<!-- Hero Section -->
<section class="section-padding bg-gradient-to-br from-primary-50 to-blue-100">
    <div class="container-custom">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">About Me</h1>
                <p class="text-lg text-gray-600 mb-6">
                    I'm a passionate web developer and software engineer with {{ $stats['years_experience'] }} years of experience 
                    creating innovative digital solutions. I love turning complex problems into simple, beautiful, and intuitive designs.
                </p>
                <p class="text-lg text-gray-600 mb-8">
                    When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, 
                    or sharing my knowledge with the developer community.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ route('contact.index') }}" class="btn-primary">
                        Let's Work Together
                    </a>
                    <a href="#" class="btn-secondary">
                        Download Resume
                    </a>
                </div>
            </div>
            <div class="text-center">
                <div class="w-64 h-64 mx-auto rounded-full bg-gradient-to-r from-primary-500 to-blue-600 p-2">
                    <div class="w-full h-full rounded-full bg-gray-200 flex items-center justify-center">
                        <svg class="w-32 h-32 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">{{ $stats['projects_completed'] }}+</div>
                <div class="text-gray-600">Projects Completed</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">{{ $stats['years_experience'] }}+</div>
                <div class="text-gray-600">Years Experience</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">{{ $stats['technologies_used'] }}+</div>
                <div class="text-gray-600">Technologies</div>
            </div>
            <div class="text-center">
                <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">{{ $stats['happy_clients'] }}+</div>
                <div class="text-gray-600">Happy Clients</div>
            </div>
        </div>
    </div>
</section>

<!-- Skills Section -->
@if($skills->count() > 0)
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Skills & Technologies</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                A comprehensive overview of my technical skills and proficiency levels
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($skills as $category => $categorySkills)
            <div class="card">
                <h3 class="text-xl font-semibold text-gray-900 mb-6 capitalize flex items-center">
                    <span class="w-3 h-3 bg-primary-600 rounded-full mr-3"></span>
                    {{ str_replace('_', ' ', $category) }}
                </h3>
                <div class="space-y-4">
                    @foreach($categorySkills as $skill)
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-700 font-medium">{{ $skill->name }}</span>
                            <span class="text-sm text-gray-500">{{ $skill->proficiency_level }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full transition-all duration-700" 
                                 style="width: {{ $skill->proficiency }}%"></div>
                        </div>
                        @if($skill->years_experience)
                        <div class="text-xs text-gray-500 mt-1">{{ $skill->years_experience }} years experience</div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Experience Section -->
@if($experiences->count() > 0)
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Work Experience</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                My professional journey and the experiences that shaped my career
            </p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="relative">
                <!-- Timeline line -->
                <div class="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-0.5 h-full bg-gray-300"></div>
                
                @foreach($experiences as $index => $experience)
                <div class="relative flex items-center mb-12 {{ $index % 2 == 0 ? 'md:flex-row' : 'md:flex-row-reverse' }}">
                    <!-- Timeline dot -->
                    <div class="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg z-10"></div>
                    
                    <!-- Content -->
                    <div class="ml-12 md:ml-0 md:w-1/2 {{ $index % 2 == 0 ? 'md:pr-12' : 'md:pl-12' }}">
                        <div class="card">
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900">{{ $experience->title }}</h3>
                                    <div class="text-primary-600 font-medium">{{ $experience->company }}</div>
                                    @if($experience->location)
                                    <div class="text-gray-500 text-sm">{{ $experience->location }}</div>
                                    @endif
                                </div>
                                @if($experience->is_current)
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Current</span>
                                @endif
                            </div>
                            
                            <div class="text-gray-600 text-sm mb-3">
                                {{ $experience->date_range }} • {{ $experience->duration }}
                            </div>
                            
                            <p class="text-gray-700 mb-4">{{ $experience->description }}</p>
                            
                            @if($experience->technologies)
                            <div class="flex flex-wrap gap-2">
                                @foreach($experience->technologies as $tech)
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">{{ $tech }}</span>
                                @endforeach
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="section-padding bg-primary-600 text-white">
    <div class="container-custom text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Let's Build Something Amazing Together</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            I'm always interested in new opportunities and exciting projects. Let's discuss how we can work together.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact.index') }}" class="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                Get In Touch
            </a>
            <a href="{{ route('projects.index') }}" class="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
                View My Work
            </a>
        </div>
    </div>
</section>
@endsection
