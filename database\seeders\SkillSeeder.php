<?php

namespace Database\Seeders;

use App\Models\Skill;
use Illuminate\Database\Seeder;

class SkillSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $skills = [
            // Frontend
            ['name' => 'HTML5', 'category' => 'frontend', 'proficiency' => 95, 'years_experience' => 8, 'is_featured' => true],
            ['name' => 'CSS3', 'category' => 'frontend', 'proficiency' => 90, 'years_experience' => 8, 'is_featured' => true],
            ['name' => 'JavaScript', 'category' => 'frontend', 'proficiency' => 90, 'years_experience' => 7, 'is_featured' => true],
            ['name' => 'React', 'category' => 'frontend', 'proficiency' => 85, 'years_experience' => 4, 'is_featured' => true],
            ['name' => 'Vue.js', 'category' => 'frontend', 'proficiency' => 80, 'years_experience' => 3, 'is_featured' => true],
            ['name' => 'Tailwind CSS', 'category' => 'frontend', 'proficiency' => 90, 'years_experience' => 3, 'is_featured' => true],
            ['name' => 'Bootstrap', 'category' => 'frontend', 'proficiency' => 85, 'years_experience' => 5],
            ['name' => 'Sass/SCSS', 'category' => 'frontend', 'proficiency' => 80, 'years_experience' => 4],
            
            // Backend
            ['name' => 'PHP', 'category' => 'backend', 'proficiency' => 90, 'years_experience' => 6, 'is_featured' => true],
            ['name' => 'Laravel', 'category' => 'backend', 'proficiency' => 95, 'years_experience' => 5, 'is_featured' => true],
            ['name' => 'Node.js', 'category' => 'backend', 'proficiency' => 80, 'years_experience' => 3, 'is_featured' => true],
            ['name' => 'Python', 'category' => 'backend', 'proficiency' => 75, 'years_experience' => 2],
            ['name' => 'Express.js', 'category' => 'backend', 'proficiency' => 75, 'years_experience' => 3],
            ['name' => 'REST APIs', 'category' => 'backend', 'proficiency' => 90, 'years_experience' => 5],
            ['name' => 'GraphQL', 'category' => 'backend', 'proficiency' => 70, 'years_experience' => 2],
            
            // Database
            ['name' => 'MySQL', 'category' => 'database', 'proficiency' => 85, 'years_experience' => 6, 'is_featured' => true],
            ['name' => 'PostgreSQL', 'category' => 'database', 'proficiency' => 80, 'years_experience' => 3],
            ['name' => 'MongoDB', 'category' => 'database', 'proficiency' => 75, 'years_experience' => 2],
            ['name' => 'Redis', 'category' => 'database', 'proficiency' => 70, 'years_experience' => 2],
            
            // DevOps
            ['name' => 'Git', 'category' => 'devops', 'proficiency' => 90, 'years_experience' => 7, 'is_featured' => true],
            ['name' => 'Docker', 'category' => 'devops', 'proficiency' => 80, 'years_experience' => 3],
            ['name' => 'AWS', 'category' => 'devops', 'proficiency' => 75, 'years_experience' => 2],
            ['name' => 'Linux', 'category' => 'devops', 'proficiency' => 80, 'years_experience' => 4],
            ['name' => 'Nginx', 'category' => 'devops', 'proficiency' => 70, 'years_experience' => 3],
            
            // Tools
            ['name' => 'VS Code', 'category' => 'tools', 'proficiency' => 95, 'years_experience' => 5],
            ['name' => 'Figma', 'category' => 'tools', 'proficiency' => 75, 'years_experience' => 3],
            ['name' => 'Postman', 'category' => 'tools', 'proficiency' => 85, 'years_experience' => 4],
            ['name' => 'Webpack', 'category' => 'tools', 'proficiency' => 70, 'years_experience' => 3],
            ['name' => 'Vite', 'category' => 'tools', 'proficiency' => 80, 'years_experience' => 2],
        ];

        foreach ($skills as $index => $skill) {
            Skill::create(array_merge($skill, ['sort_order' => $index]));
        }
    }
}
