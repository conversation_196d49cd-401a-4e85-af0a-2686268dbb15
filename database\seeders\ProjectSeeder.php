<?php

namespace Database\Seeders;

use App\Models\Project;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $projects = [
            [
                'title' => 'E-Commerce Platform',
                'slug' => 'e-commerce-platform',
                'description' => 'A full-featured e-commerce platform built with <PERSON><PERSON> and React, featuring user authentication, product management, shopping cart, and payment integration.',
                'content' => "This comprehensive e-commerce platform was built to handle high-traffic online stores with advanced features.\n\nKey Features:\n- User registration and authentication\n- Product catalog with categories and filters\n- Shopping cart and wishlist functionality\n- Secure payment processing with Stripe\n- Order management and tracking\n- Admin dashboard for inventory management\n- Responsive design for mobile and desktop\n\nThe backend API was built with <PERSON><PERSON>, providing robust data management and security. The frontend uses React with modern hooks and context for state management. The application is deployed on AWS with auto-scaling capabilities.",
                'technologies' => ['Laravel', 'React', 'MySQL', 'Stripe API', 'AWS', 'Docker', 'Redis'],
                'github_url' => 'https://github.com/johndoe/ecommerce-platform',
                'live_url' => 'https://ecommerce-demo.example.com',
                'status' => 'completed',
                'start_date' => Carbon::parse('2023-01-15'),
                'end_date' => Carbon::parse('2023-06-30'),
                'featured' => true,
                'sort_order' => 1,
                'is_published' => true,
            ],
            [
                'title' => 'Task Management App',
                'slug' => 'task-management-app',
                'description' => 'A collaborative task management application with real-time updates, team collaboration features, and project tracking capabilities.',
                'content' => "A modern task management solution designed for teams and individuals to organize their work efficiently.\n\nFeatures:\n- Create and organize tasks with priorities and due dates\n- Team collaboration with comments and file attachments\n- Real-time notifications and updates\n- Project timelines and progress tracking\n- Time tracking and reporting\n- Kanban board view\n- Calendar integration\n\nBuilt with Vue.js for a reactive user interface and Laravel for the backend API. Uses WebSocket connections for real-time updates and notifications.",
                'technologies' => ['Vue.js', 'Laravel', 'WebSocket', 'MySQL', 'Tailwind CSS', 'Pusher'],
                'github_url' => 'https://github.com/johndoe/task-manager',
                'live_url' => 'https://taskmanager-demo.example.com',
                'status' => 'completed',
                'start_date' => Carbon::parse('2022-08-01'),
                'end_date' => Carbon::parse('2022-12-15'),
                'featured' => true,
                'sort_order' => 2,
                'is_published' => true,
            ],
            [
                'title' => 'Restaurant Booking System',
                'slug' => 'restaurant-booking-system',
                'description' => 'An online reservation system for restaurants with table management, customer notifications, and analytics dashboard.',
                'content' => "A comprehensive booking system that helps restaurants manage reservations and optimize table utilization.\n\nCore Features:\n- Online table reservation with availability checking\n- Customer management and history\n- SMS and email notifications\n- Table layout and capacity management\n- Waitlist functionality\n- Analytics and reporting\n- Multi-location support\n\nThe system integrates with popular POS systems and provides a mobile-friendly interface for both customers and restaurant staff.",
                'technologies' => ['Laravel', 'Alpine.js', 'MySQL', 'Twilio API', 'Chart.js', 'Bootstrap'],
                'github_url' => 'https://github.com/johndoe/restaurant-booking',
                'live_url' => 'https://booking-demo.example.com',
                'status' => 'completed',
                'start_date' => Carbon::parse('2022-03-01'),
                'end_date' => Carbon::parse('2022-07-20'),
                'featured' => true,
                'sort_order' => 3,
                'is_published' => true,
            ],
            [
                'title' => 'Portfolio Website',
                'slug' => 'portfolio-website',
                'description' => 'A responsive portfolio website showcasing web development projects with modern design and smooth animations.',
                'content' => "A personal portfolio website designed to showcase development skills and projects.\n\nFeatures:\n- Responsive design with mobile-first approach\n- Smooth scrolling and animations\n- Project showcase with filtering\n- Contact form with validation\n- Blog section for articles\n- SEO optimized\n- Fast loading with optimized assets\n\nBuilt with modern web technologies focusing on performance and user experience.",
                'technologies' => ['HTML5', 'CSS3', 'JavaScript', 'Tailwind CSS', 'Alpine.js'],
                'github_url' => 'https://github.com/johndoe/portfolio',
                'live_url' => 'https://johndoe.example.com',
                'status' => 'completed',
                'start_date' => Carbon::parse('2021-11-01'),
                'end_date' => Carbon::parse('2021-12-30'),
                'featured' => false,
                'sort_order' => 4,
                'is_published' => true,
            ],
            [
                'title' => 'Weather Dashboard',
                'slug' => 'weather-dashboard',
                'description' => 'A weather monitoring dashboard with real-time data, forecasts, and interactive maps using weather APIs.',
                'content' => "An interactive weather dashboard providing comprehensive weather information and forecasts.\n\nFeatures:\n- Current weather conditions for multiple locations\n- 7-day weather forecast\n- Interactive weather maps\n- Weather alerts and notifications\n- Historical weather data\n- Customizable dashboard widgets\n- Location-based weather detection\n\nIntegrates with multiple weather APIs to provide accurate and up-to-date information.",
                'technologies' => ['React', 'Node.js', 'Express', 'Weather API', 'Mapbox', 'Chart.js'],
                'github_url' => 'https://github.com/johndoe/weather-dashboard',
                'live_url' => 'https://weather-demo.example.com',
                'status' => 'completed',
                'start_date' => Carbon::parse('2021-06-01'),
                'end_date' => Carbon::parse('2021-08-15'),
                'featured' => false,
                'sort_order' => 5,
                'is_published' => true,
            ],
            [
                'title' => 'Social Media Analytics Tool',
                'slug' => 'social-media-analytics',
                'description' => 'A comprehensive analytics platform for social media management with reporting and scheduling features.',
                'content' => "An all-in-one social media management and analytics platform for businesses and marketers.\n\nKey Features:\n- Multi-platform social media integration\n- Post scheduling and automation\n- Engagement analytics and reporting\n- Competitor analysis\n- Content performance tracking\n- Team collaboration tools\n- Custom dashboard creation\n\nSupports major social media platforms including Facebook, Twitter, Instagram, and LinkedIn.",
                'technologies' => ['Laravel', 'Vue.js', 'MySQL', 'Redis', 'Social Media APIs', 'Chart.js'],
                'github_url' => 'https://github.com/johndoe/social-analytics',
                'live_url' => null,
                'status' => 'in_progress',
                'start_date' => Carbon::parse('2023-09-01'),
                'end_date' => null,
                'featured' => false,
                'sort_order' => 6,
                'is_published' => true,
            ],
        ];

        foreach ($projects as $project) {
            Project::create($project);
        }
    }
}
