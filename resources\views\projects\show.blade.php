@extends('layouts.app')

@section('title', $project->title . ' - Portfolio')
@section('description', $project->description)

@section('content')
<!-- Project Hero -->
<section class="section-padding bg-gradient-to-br from-primary-50 to-blue-100">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center space-x-2 text-sm text-gray-600">
                    <li><a href="{{ route('home') }}" class="hover:text-primary-600">Home</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('projects.index') }}" class="hover:text-primary-600">Projects</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li class="text-gray-900">{{ $project->title }}</li>
                </ol>
            </nav>
            
            <!-- Project Header -->
            <div class="text-center mb-8">
                <div class="flex items-center justify-center gap-4 mb-4">
                    <span class="px-3 py-1 text-sm rounded-full 
                        {{ $project->status === 'completed' ? 'bg-green-100 text-green-800' : '' }}
                        {{ $project->status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : '' }}
                        {{ $project->status === 'planned' ? 'bg-blue-100 text-blue-800' : '' }}">
                        {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                    </span>
                    @if($project->featured)
                    <span class="px-3 py-1 bg-primary-100 text-primary-800 text-sm rounded-full">Featured</span>
                    @endif
                </div>
                
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">{{ $project->title }}</h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">{{ $project->description }}</p>
                
                <!-- Project Links -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                    @if($project->live_url)
                    <a href="{{ $project->live_url }}" target="_blank" class="btn-primary">
                        View Live Demo ↗
                    </a>
                    @endif
                    @if($project->github_url)
                    <a href="{{ $project->github_url }}" target="_blank" class="btn-secondary">
                        View Code on GitHub ↗
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Details -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Project Image -->
                    @if($project->image_url)
                    <div class="mb-8">
                        <img src="{{ $project->image_url }}" alt="{{ $project->title }}" 
                             class="w-full rounded-lg shadow-lg">
                    </div>
                    @endif
                    
                    <!-- Project Content -->
                    @if($project->content)
                    <div class="prose prose-lg max-w-none mb-8">
                        {!! nl2br(e($project->content)) !!}
                    </div>
                    @endif
                    
                    <!-- Project Gallery -->
                    @if($project->gallery_urls && count($project->gallery_urls) > 0)
                    <div class="mb-8">
                        <h3 class="text-2xl font-semibold text-gray-900 mb-6">Project Gallery</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($project->gallery_urls as $image)
                            <div class="overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow">
                                <img src="{{ $image }}" alt="Project gallery image" 
                                     class="w-full h-48 object-cover hover:scale-105 transition-transform duration-300">
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
                
                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24 space-y-6">
                        <!-- Project Info -->
                        <div class="card">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Info</h3>
                            <div class="space-y-3">
                                @if($project->start_date)
                                <div>
                                    <span class="text-gray-600 text-sm">Duration:</span>
                                    <div class="font-medium">
                                        {{ $project->start_date->format('M Y') }} - 
                                        {{ $project->end_date ? $project->end_date->format('M Y') : 'Present' }}
                                        @if($project->duration)
                                        <span class="text-gray-500 text-sm">({{ $project->duration }})</span>
                                        @endif
                                    </div>
                                </div>
                                @endif
                                
                                <div>
                                    <span class="text-gray-600 text-sm">Status:</span>
                                    <div class="font-medium capitalize">{{ str_replace('_', ' ', $project->status) }}</div>
                                </div>
                                
                                @if($project->live_url)
                                <div>
                                    <span class="text-gray-600 text-sm">Live URL:</span>
                                    <div>
                                        <a href="{{ $project->live_url }}" target="_blank" 
                                           class="text-primary-600 hover:text-primary-700 font-medium break-all">
                                            {{ parse_url($project->live_url, PHP_URL_HOST) }} ↗
                                        </a>
                                    </div>
                                </div>
                                @endif
                                
                                @if($project->github_url)
                                <div>
                                    <span class="text-gray-600 text-sm">Repository:</span>
                                    <div>
                                        <a href="{{ $project->github_url }}" target="_blank" 
                                           class="text-primary-600 hover:text-primary-700 font-medium">
                                            GitHub ↗
                                        </a>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Technologies -->
                        @if($project->technologies)
                        <div class="card">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Technologies Used</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach($project->technologies as $tech)
                                <span class="px-3 py-1 bg-primary-100 text-primary-700 text-sm rounded-full">
                                    {{ $tech }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                        @endif
                        
                        <!-- Share -->
                        <div class="card">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Share Project</h3>
                            <div class="flex gap-3">
                                <a href="https://twitter.com/intent/tweet?text={{ urlencode($project->title) }}&url={{ urlencode(request()->url()) }}" 
                                   target="_blank" class="text-gray-600 hover:text-blue-500 transition-colors">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}" 
                                   target="_blank" class="text-gray-600 hover:text-blue-700 transition-colors">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Projects -->
@if($relatedProjects->count() > 0)
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Related Projects</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @foreach($relatedProjects as $relatedProject)
                <div class="card group hover:shadow-lg transition-shadow duration-300">
                    @if($relatedProject->image_url)
                    <div class="mb-4 overflow-hidden rounded-lg">
                        <img src="{{ $relatedProject->image_url }}" alt="{{ $relatedProject->title }}" 
                             class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    @endif
                    
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $relatedProject->title }}</h3>
                    <p class="text-gray-600 text-sm mb-4">{{ Str::limit($relatedProject->description, 80) }}</p>
                    
                    <a href="{{ route('projects.show', $relatedProject) }}" 
                       class="text-primary-600 hover:text-primary-700 font-medium text-sm">
                        View Project →
                    </a>
                </div>
                @endforeach
            </div>
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="section-padding bg-primary-600 text-white">
    <div class="container-custom text-center">
        <h2 class="text-3xl font-bold mb-4">Interested in Working Together?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            I'm always open to discussing new projects and opportunities.
        </p>
        <a href="{{ route('contact.index') }}" class="bg-white text-primary-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg transition-colors duration-200">
            Get In Touch
        </a>
    </div>
</section>
@endsection
