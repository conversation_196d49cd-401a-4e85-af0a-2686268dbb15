@extends('layouts.app')

@section('title', 'Projects - Portfolio')
@section('description', 'Browse through my portfolio of web development and software engineering projects.')

@section('content')
<!-- Hero Section -->
<section class="section-padding bg-gradient-to-br from-primary-50 to-blue-100">
    <div class="container-custom">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">My Projects</h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                A showcase of my work in web development and software engineering. 
                Each project represents a unique challenge and learning experience.
            </p>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="py-8 bg-white border-b">
    <div class="container-custom">
        <form method="GET" action="{{ route('projects.index') }}" class="flex flex-wrap gap-4 items-center justify-between">
            <!-- Search -->
            <div class="flex-1 min-w-64">
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="Search projects..." 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            
            <!-- Technology Filter -->
            <div class="min-w-48">
                <select name="technology" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">All Technologies</option>
                    @foreach($allTechnologies as $tech)
                    <option value="{{ $tech }}" {{ request('technology') == $tech ? 'selected' : '' }}>
                        {{ $tech }}
                    </option>
                    @endforeach
                </select>
            </div>
            
            <!-- Status Filter -->
            <div class="min-w-40">
                <select name="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">All Status</option>
                    @foreach($statuses as $value => $label)
                    <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                        {{ $label }}
                    </option>
                    @endforeach
                </select>
            </div>
            
            <!-- Filter Button -->
            <div class="flex gap-2">
                <button type="submit" class="btn-primary">
                    Filter
                </button>
                @if(request()->hasAny(['search', 'technology', 'status']))
                <a href="{{ route('projects.index') }}" class="btn-secondary">
                    Clear
                </a>
                @endif
            </div>
        </form>
    </div>
</section>

<!-- Projects Grid -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        @if($projects->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($projects as $project)
            <div class="card group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <!-- Project Image -->
                @if($project->image_url)
                <div class="mb-6 overflow-hidden rounded-lg">
                    <img src="{{ $project->image_url }}" alt="{{ $project->title }}" 
                         class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                </div>
                @else
                <div class="mb-6 h-48 bg-gradient-to-br from-primary-100 to-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-16 h-16 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                </div>
                @endif
                
                <!-- Project Status Badge -->
                <div class="flex items-center justify-between mb-3">
                    <span class="px-2 py-1 text-xs rounded-full 
                        {{ $project->status === 'completed' ? 'bg-green-100 text-green-800' : '' }}
                        {{ $project->status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : '' }}
                        {{ $project->status === 'planned' ? 'bg-blue-100 text-blue-800' : '' }}">
                        {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                    </span>
                    @if($project->featured)
                    <span class="px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full">Featured</span>
                    @endif
                </div>
                
                <!-- Project Title -->
                <h3 class="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
                    {{ $project->title }}
                </h3>
                
                <!-- Project Description -->
                <p class="text-gray-600 mb-4 line-clamp-3">{{ $project->description }}</p>
                
                <!-- Technologies -->
                @if($project->technologies)
                <div class="flex flex-wrap gap-2 mb-6">
                    @foreach(array_slice($project->technologies, 0, 4) as $tech)
                    <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">{{ $tech }}</span>
                    @endforeach
                    @if(count($project->technologies) > 4)
                    <span class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        +{{ count($project->technologies) - 4 }} more
                    </span>
                    @endif
                </div>
                @endif
                
                <!-- Project Links -->
                <div class="flex items-center justify-between">
                    <a href="{{ route('projects.show', $project) }}" 
                       class="text-primary-600 hover:text-primary-700 font-medium transition-colors">
                        View Details →
                    </a>
                    <div class="flex gap-3">
                        @if($project->github_url)
                        <a href="{{ $project->github_url }}" target="_blank" 
                           class="text-gray-500 hover:text-gray-700 transition-colors" title="View Code">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                        @endif
                        @if($project->live_url)
                        <a href="{{ $project->live_url }}" target="_blank" 
                           class="text-gray-500 hover:text-gray-700 transition-colors" title="Live Demo">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                            </svg>
                        </a>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <!-- Pagination -->
        @if($projects->hasPages())
        <div class="mt-12">
            {{ $projects->appends(request()->query())->links() }}
        </div>
        @endif
        
        @else
        <!-- No Projects Found -->
        <div class="text-center py-16">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Projects Found</h3>
            <p class="text-gray-600 mb-6">
                @if(request()->hasAny(['search', 'technology', 'status']))
                    No projects match your current filters. Try adjusting your search criteria.
                @else
                    Projects will be displayed here once they are added.
                @endif
            </p>
            @if(request()->hasAny(['search', 'technology', 'status']))
            <a href="{{ route('projects.index') }}" class="btn-primary">
                Clear Filters
            </a>
            @endif
        </div>
        @endif
    </div>
</section>
@endsection
