<?php

namespace Database\Seeders;

use App\Models\Experience;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ExperienceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $experiences = [
            [
                'title' => 'Senior Full Stack Developer',
                'company' => 'TechCorp Solutions',
                'company_url' => 'https://techcorp.example.com',
                'location' => 'San Francisco, CA',
                'description' => 'Lead development of enterprise web applications using Laravel and React. Managed a team of 4 developers and collaborated with product managers to deliver high-quality software solutions.',
                'responsibilities' => [
                    'Led development of 3 major web applications serving 10,000+ users',
                    'Mentored junior developers and conducted code reviews',
                    'Implemented CI/CD pipelines reducing deployment time by 60%',
                    'Collaborated with UX/UI designers to improve user experience',
                    'Optimized database queries resulting in 40% performance improvement'
                ],
                'technologies' => ['Laravel', 'React', 'MySQL', 'Docker', 'AWS', 'Git'],
                'start_date' => Carbon::parse('2022-01-01'),
                'end_date' => null,
                'is_current' => true,
                'employment_type' => 'full_time',
                'sort_order' => 1,
            ],
            [
                'title' => 'Full Stack Developer',
                'company' => 'Digital Innovations Inc',
                'company_url' => 'https://digitalinnovations.example.com',
                'location' => 'Remote',
                'description' => 'Developed and maintained multiple client websites and web applications. Worked closely with clients to understand requirements and deliver custom solutions.',
                'responsibilities' => [
                    'Built 15+ responsive websites using modern web technologies',
                    'Integrated third-party APIs and payment gateways',
                    'Implemented SEO best practices improving search rankings',
                    'Provided technical support and maintenance for existing projects',
                    'Collaborated with design team to ensure pixel-perfect implementations'
                ],
                'technologies' => ['PHP', 'Laravel', 'Vue.js', 'MySQL', 'JavaScript', 'CSS3'],
                'start_date' => Carbon::parse('2020-03-01'),
                'end_date' => Carbon::parse('2021-12-31'),
                'is_current' => false,
                'employment_type' => 'full_time',
                'sort_order' => 2,
            ],
            [
                'title' => 'Frontend Developer',
                'company' => 'StartupXYZ',
                'company_url' => 'https://startupxyz.example.com',
                'location' => 'Austin, TX',
                'description' => 'Focused on frontend development for a fast-growing SaaS platform. Worked in an agile environment with rapid iteration and deployment cycles.',
                'responsibilities' => [
                    'Developed responsive user interfaces using React and Tailwind CSS',
                    'Implemented real-time features using WebSocket connections',
                    'Optimized application performance and loading times',
                    'Participated in daily standups and sprint planning',
                    'Conducted user testing and gathered feedback for improvements'
                ],
                'technologies' => ['React', 'JavaScript', 'Tailwind CSS', 'Node.js', 'MongoDB'],
                'start_date' => Carbon::parse('2019-06-01'),
                'end_date' => Carbon::parse('2020-02-28'),
                'is_current' => false,
                'employment_type' => 'full_time',
                'sort_order' => 3,
            ],
            [
                'title' => 'Junior Web Developer',
                'company' => 'WebDev Agency',
                'company_url' => 'https://webdevagency.example.com',
                'location' => 'Los Angeles, CA',
                'description' => 'Started my professional career developing websites for small to medium businesses. Gained experience in various web technologies and client communication.',
                'responsibilities' => [
                    'Created custom WordPress themes and plugins',
                    'Built static websites using HTML, CSS, and JavaScript',
                    'Assisted senior developers with larger projects',
                    'Communicated with clients to gather requirements',
                    'Performed website maintenance and updates'
                ],
                'technologies' => ['HTML5', 'CSS3', 'JavaScript', 'PHP', 'WordPress', 'MySQL'],
                'start_date' => Carbon::parse('2018-01-01'),
                'end_date' => Carbon::parse('2019-05-31'),
                'is_current' => false,
                'employment_type' => 'full_time',
                'sort_order' => 4,
            ],
        ];

        foreach ($experiences as $experience) {
            Experience::create($experience);
        }
    }
}
